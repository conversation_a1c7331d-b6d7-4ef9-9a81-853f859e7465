[{"strategy_id": "crossover_6a115226", "name": "Strategy LS_NIFTY_25200_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25200_20250718192943 and Strategy ATMLC_NIFTY_25000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_fd9f80bf", "name": "Strategy LS_NIFTY_24900_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24900_20250718192943 and Strategy LP_NIFTY_24800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_36983542", "name": "Strategy LC_NIFTY_25700_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25700_20250718192943 and Strategy LP_NIFTY_25400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}]