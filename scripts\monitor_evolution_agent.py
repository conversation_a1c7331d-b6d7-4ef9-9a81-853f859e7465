#!/usr/bin/env python3
"""
Performance Monitor for Options Strategy Evolution Agent
Tracks efficiency metrics and provides optimization recommendations
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
import sys
import psutil
import os

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EvolutionAgentMonitor:
    """Monitor for Strategy Evolution Agent performance"""
    
    def __init__(self):
        self.data_path = Path("data")
        self.evolution_path = self.data_path / "strategy_evolution"
        self.monitoring_data = []
        self.start_time = time.time()
        
    def get_process_info(self, pid: int = None) -> dict:
        """Get process information for the evolution agent"""
        try:
            if pid is None:
                # Find Python processes
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        if 'python' in proc.info['name'].lower():
                            cmdline = ' '.join(proc.info['cmdline'] or [])
                            if 'main.py' in cmdline or 'evolution' in cmdline:
                                pid = proc.info['pid']
                                break
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
            
            if pid:
                process = psutil.Process(pid)
                return {
                    'pid': pid,
                    'cpu_percent': process.cpu_percent(),
                    'memory_mb': process.memory_info().rss / 1024 / 1024,
                    'memory_percent': process.memory_percent(),
                    'num_threads': process.num_threads(),
                    'status': process.status(),
                    'create_time': datetime.fromtimestamp(process.create_time()),
                    'runtime_minutes': (time.time() - process.create_time()) / 60
                }
        except Exception as e:
            logger.error(f"Failed to get process info: {e}")
        
        return {}
    
    def analyze_strategy_registry(self) -> dict:
        """Analyze strategy registry for efficiency metrics"""
        try:
            registry_file = self.evolution_path / "registry" / "strategy_registry.json"
            if not registry_file.exists():
                return {}
            
            with open(registry_file, 'r') as f:
                registry = json.load(f)
            
            status_counts = {}
            creation_times = []
            
            for strategy_id, strategy_data in registry.items():
                status = strategy_data.get('status', 'unknown')
                status_counts[status] = status_counts.get(status, 0) + 1
                
                if 'created_at' in strategy_data:
                    try:
                        created_at = datetime.fromisoformat(strategy_data['created_at'])
                        creation_times.append(created_at)
                    except:
                        pass
            
            # Calculate creation rate
            if creation_times:
                creation_times.sort()
                time_span = (creation_times[-1] - creation_times[0]).total_seconds() / 3600  # hours
                creation_rate = len(creation_times) / max(time_span, 1)  # strategies per hour
            else:
                creation_rate = 0
            
            return {
                'total_strategies': len(registry),
                'status_distribution': status_counts,
                'creation_rate_per_hour': creation_rate,
                'latest_creation': creation_times[-1].isoformat() if creation_times else None
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze strategy registry: {e}")
            return {}
    
    def analyze_performance_history(self) -> dict:
        """Analyze performance history for trends"""
        try:
            perf_file = self.evolution_path / "performance_history.json"
            if not perf_file.exists():
                return {}
            
            with open(perf_file, 'r') as f:
                history = json.load(f)
            
            total_records = sum(len(metrics) for metrics in history.values())
            strategies_with_history = len(history)
            
            return {
                'strategies_with_performance_data': strategies_with_history,
                'total_performance_records': total_records,
                'avg_records_per_strategy': total_records / max(strategies_with_history, 1)
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze performance history: {e}")
            return {}
    
    def analyze_evolution_events(self) -> dict:
        """Analyze evolution events for activity metrics"""
        try:
            events_file = self.evolution_path / "evolution_history.json"
            if not events_file.exists():
                return {}
            
            with open(events_file, 'r') as f:
                events = json.load(f)
            
            if not events:
                return {'total_events': 0}
            
            # Analyze event types and timing
            event_types = {}
            recent_events = 0
            cutoff_time = datetime.now() - timedelta(hours=1)
            
            for event in events:
                reason = event.get('reason', 'unknown')
                event_types[reason] = event_types.get(reason, 0) + 1
                
                try:
                    event_time = datetime.fromisoformat(event.get('timestamp', ''))
                    if event_time > cutoff_time:
                        recent_events += 1
                except:
                    pass
            
            return {
                'total_events': len(events),
                'event_types': event_types,
                'recent_events_1h': recent_events
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze evolution events: {e}")
            return {}
    
    def check_file_sizes(self) -> dict:
        """Check file sizes for storage efficiency"""
        try:
            file_sizes = {}
            
            for file_path in self.evolution_path.rglob("*.json"):
                try:
                    size_mb = file_path.stat().st_size / 1024 / 1024
                    file_sizes[str(file_path.relative_to(self.evolution_path))] = round(size_mb, 2)
                except:
                    pass
            
            total_size = sum(file_sizes.values())
            
            return {
                'total_size_mb': round(total_size, 2),
                'file_sizes': file_sizes,
                'largest_files': sorted(file_sizes.items(), key=lambda x: x[1], reverse=True)[:5]
            }
            
        except Exception as e:
            logger.error(f"Failed to check file sizes: {e}")
            return {}
    
    def generate_efficiency_report(self, pid: int = None) -> dict:
        """Generate comprehensive efficiency report"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'monitor_runtime_minutes': (time.time() - self.start_time) / 60,
            'process_info': self.get_process_info(pid),
            'strategy_analysis': self.analyze_strategy_registry(),
            'performance_analysis': self.analyze_performance_history(),
            'evolution_analysis': self.analyze_evolution_events(),
            'storage_analysis': self.check_file_sizes()
        }
        
        # Add efficiency recommendations
        recommendations = []
        
        # Check strategy creation rate
        creation_rate = report['strategy_analysis'].get('creation_rate_per_hour', 0)
        if creation_rate > 50:
            recommendations.append("HIGH: Strategy creation rate is very high - consider reducing genetic algorithm frequency")
        elif creation_rate > 20:
            recommendations.append("MEDIUM: Strategy creation rate is moderate - monitor for population explosion")
        
        # Check memory usage
        memory_mb = report['process_info'].get('memory_mb', 0)
        if memory_mb > 1000:
            recommendations.append("HIGH: Memory usage is high - implement more aggressive cleanup")
        elif memory_mb > 500:
            recommendations.append("MEDIUM: Memory usage is moderate - monitor for memory leaks")
        
        # Check file sizes
        total_size = report['storage_analysis'].get('total_size_mb', 0)
        if total_size > 100:
            recommendations.append("MEDIUM: Storage usage is high - consider data archiving")
        
        # Check CPU usage
        cpu_percent = report['process_info'].get('cpu_percent', 0)
        if cpu_percent > 80:
            recommendations.append("HIGH: CPU usage is very high - optimize computational loops")
        elif cpu_percent > 50:
            recommendations.append("MEDIUM: CPU usage is moderate - consider parallel processing")
        
        report['recommendations'] = recommendations
        
        return report
    
    def save_report(self, report: dict, filename: str = None):
        """Save efficiency report to file"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"efficiency_report_{timestamp}.json"
        
        report_path = self.evolution_path / "reports"
        report_path.mkdir(exist_ok=True)
        
        with open(report_path / filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Efficiency report saved to {report_path / filename}")

def main():
    """Main monitoring function"""
    print("Options Strategy Evolution Agent - Performance Monitor")
    print("=" * 60)
    
    monitor = EvolutionAgentMonitor()
    
    # Get PID from command line if provided
    pid = None
    if len(sys.argv) > 1:
        try:
            pid = int(sys.argv[1])
            print(f"Monitoring process PID: {pid}")
        except ValueError:
            print("Invalid PID provided, will auto-detect")
    
    # Generate efficiency report
    report = monitor.generate_efficiency_report(pid)
    
    # Display summary
    print("\n📊 EFFICIENCY SUMMARY")
    print("-" * 30)
    
    process_info = report.get('process_info', {})
    if process_info:
        print(f"Process PID: {process_info.get('pid', 'N/A')}")
        print(f"Runtime: {process_info.get('runtime_minutes', 0):.1f} minutes")
        print(f"Memory Usage: {process_info.get('memory_mb', 0):.1f} MB")
        print(f"CPU Usage: {process_info.get('cpu_percent', 0):.1f}%")
    
    strategy_info = report.get('strategy_analysis', {})
    print(f"Total Strategies: {strategy_info.get('total_strategies', 0)}")
    print(f"Creation Rate: {strategy_info.get('creation_rate_per_hour', 0):.1f} strategies/hour")
    
    storage_info = report.get('storage_analysis', {})
    print(f"Storage Usage: {storage_info.get('total_size_mb', 0):.1f} MB")
    
    # Display recommendations
    recommendations = report.get('recommendations', [])
    if recommendations:
        print("\n⚠️  RECOMMENDATIONS")
        print("-" * 30)
        for rec in recommendations:
            print(f"• {rec}")
    else:
        print("\n✅ No efficiency issues detected")
    
    # Save detailed report
    monitor.save_report(report)
    
    print(f"\n📄 Detailed report saved to data/strategy_evolution/reports/")

if __name__ == "__main__":
    main()
