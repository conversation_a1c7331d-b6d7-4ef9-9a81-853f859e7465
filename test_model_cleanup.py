#!/usr/bin/env python3
"""
Test script to demonstrate the improved model management in the AI training agent.
This script shows how the new implementation prevents model file proliferation.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent))

from agents.options_ai_training_agent import OptionsAITrainingAgent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_model_cleanup():
    """Test the model cleanup functionality"""
    try:
        logger.info("=== Testing Model Cleanup Functionality ===")
        
        # Initialize the agent
        agent = OptionsAITrainingAgent()
        
        # Initialize with incremental mode (this will trigger cleanup)
        success = await agent.initialize(incremental=True)
        if not success:
            logger.error("Failed to initialize agent")
            return False
        
        # Count remaining model files
        models_path = Path("data/models")
        if models_path.exists():
            remaining_files = list(models_path.glob("*.joblib"))
            logger.info(f"Remaining model files after cleanup: {len(remaining_files)}")
            
            # Show examples of remaining files
            for i, file in enumerate(remaining_files[:5]):  # Show first 5
                logger.info(f"  Example {i+1}: {file.name}")
            
            if len(remaining_files) > 5:
                logger.info(f"  ... and {len(remaining_files) - 5} more files")
        else:
            logger.info("Models directory does not exist")
        
        # Cleanup agent
        await agent.cleanup()
        
        logger.info("=== Model Cleanup Test Completed ===")
        return True
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return False

async def demonstrate_new_naming():
    """Demonstrate the new clean naming convention"""
    try:
        logger.info("=== Demonstrating New Model Naming Convention ===")
        
        agent = OptionsAITrainingAgent()
        
        # Test the clean name function
        test_names = [
            "strategy_return_sgd_20250813_082853_20250814_234349_20250815_090200",
            "option_price_lightgbm_20250719_173852_20250814_225433",
            "implied_vol_sgd",
            "sharpe_ratio_lightgbm_20250815_123456"
        ]
        
        logger.info("Testing clean name extraction:")
        for name in test_names:
            clean_name = agent._get_clean_model_name(name)
            logger.info(f"  Original: {name}")
            logger.info(f"  Clean:    {clean_name}")
            logger.info("")
        
        logger.info("=== New Naming Convention Demo Completed ===")
        return True
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        return False

async def main():
    """Main test function"""
    logger.info("Starting model management improvement tests...")
    
    # Test cleanup functionality
    cleanup_success = await test_model_cleanup()
    
    # Demonstrate new naming
    naming_success = await demonstrate_new_naming()
    
    if cleanup_success and naming_success:
        logger.info("✅ All tests completed successfully!")
        logger.info("\n🎯 Key Improvements:")
        logger.info("1. ✅ Automatic cleanup of problematic model files with accumulated timestamps")
        logger.info("2. ✅ Clean model naming without timestamp accumulation")
        logger.info("3. ✅ Proper incremental training that updates existing models")
        logger.info("4. ✅ Backup system for model safety")
        logger.info("5. ✅ Configurable retention policy for old models")
        
        logger.info("\n📋 Best Practices Implemented:")
        logger.info("• Single model file per model type (for incremental training)")
        logger.info("• Automatic backup before model updates")
        logger.info("• Clean naming convention without timestamp accumulation")
        logger.info("• Proper model versioning strategy")
        logger.info("• Automatic cleanup of old/problematic files")
        
    else:
        logger.error("❌ Some tests failed!")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
