[{"strategy_id": "crossover_21337948", "name": "Strategy LC_NIFTY_24300_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24300_20250718192943 and Strategy LST_NIFTY_25100_24200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_92a5eafc", "name": "Strategy LC_NIFTY_24000_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24000_20250718192943 and Strategy LST_NIFTY_25300_24200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_5151d0a0", "name": "Strategy LP_NIFTY_25800_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25800_20250718192943 and Strategy LP_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_26784dc7", "name": "Strategy ATMLP_NIFTY_25400_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_25400_20250718192943 and Strategy LS_NIFTY_24200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_3ed00ea9", "name": "Strategy LC_NIFTY_25700_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25700_20250718192943 and Strategy LC_NIFTY_24300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_c9994c73", "name": "Strategy LS_NIFTY_24800_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24800_20250718192943 and Strategy ATMLP_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_5ab937bb", "name": "Strategy LC_NIFTY_24900_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24900_20250718192943 and Strategy LST_NIFTY_25100_24000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_1dd55428", "name": "Strategy LC_NIFTY_24800_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24800_20250718192943 and Strategy LC_NIFTY_25600_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_0fe00657", "name": "Strategy LST_NIFTY_25300_24100_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25300_24100_20250718192943 and Strategy LS_NIFTY_25500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_980a32b2", "name": "Strategy ATMLP_NIFTY_24800_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_24800_20250718192943 and Strategy LST_NIFTY_25100_24100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_63567282", "name": "Strategy LS_NIFTY_24800_20250718192943", "description": "Crossover of Strategy LS_NIFTY_24800_20250718192943 and Strategy LP_NIFTY_24000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_e2f6c32b", "name": "Strategy LST_NIFTY_25100_24100_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25100_24100_20250718192943 and Strategy LC_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_83346b5a", "name": "Strategy LC_NIFTY_24900_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24900_20250718192943 and Strategy LC_NIFTY_24900_20250718192943", "parameters": {}, "entry_conditions": ["iv_rank < 70"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.018772732522900393, "take_profit": 0.04758630260371616}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_222b213d", "name": "Strategy LC_NIFTY_24900_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24900_20250718192943 and Strategy ATMLC_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": ["iv_rank < 70"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.018772732522900393, "take_profit": 0.04758630260371616}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_f0464d82", "name": "Strategy LC_NIFTY_24900_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24900_20250718192943 and Strategy ATMLC_NIFTY_24900_20250718192943", "parameters": {}, "entry_conditions": ["iv_rank < 70"], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.018772732522900393, "take_profit": 0.04758630260371616}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_f80e5c6e", "name": "Strategy LST_NIFTY_25300_24100_20250718192943", "description": "Crossover of Strategy LST_NIFTY_25300_24100_20250718192943 and Strategy LP_NIFTY_25800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_a3894f74", "name": "Strategy LS_NIFTY_25200_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25200_20250718192943 and Strategy LST_NIFTY_25100_24100_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_879bd53d", "name": "Strategy LS_NIFTY_25500_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25500_20250718192943 and Strategy LP_NIFTY_24900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_4c396133", "name": "Strategy LC_NIFTY_24100_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24100_20250718192943 and Strategy LP_NIFTY_25800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.023732327194404674, "take_profit": 0.04145111077683508}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}]